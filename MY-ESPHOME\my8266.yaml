esphome:
  name: myesphome8266
  on_boot:
    priority: -100  # 启动优先级（可选）
    then:
      - delay: 5s
      - logger.log: "系统上电后，已等待5秒。现在开始工作"
      - switch.turn_on: some_output
esp8266:
  board: nodemcuv2

wifi:
  ssid: "HOME"
  password: "nb9d30@24zd"

sensor:
- platform: dht
  pin: D2
  temperature:
    name: "Bed Room Temperature"
  humidity:
    name: "Bed Room Humidity"
  update_interval: 60s
  model: DHT11

logger:
  baud_rate: 115200
ota:
  platform: esphome
api:

web_server:
  port: 80