esphome:
  name: myesphome8266
  on_boot:
    priority: -100  # 启动优先级（可选）
    then:
      - delay: 5s
      - logger.log: "系统上电后，已等待5秒。现在开始工作"
      - switch.turn_on: some_output
esp8266:
  board: nodemcuv2

wifi:
  ssid: "HOME"
  password: "nb9d30@24zd"

# I2C配置 - SHT30需要I2C通信
i2c:
  sda: D1  # SDA引脚 (GPIO5)
  scl: D2  # SCL引脚 (GPIO4)
  scan: true  # 启动时扫描I2C设备

sensor:
# DHT11传感器 (保留原有配置)
- platform: dht
  pin: D3  # 改为D3引脚，因为D2现在用于I2C SCL
  temperature:
    name: "Bed Room Temperature DHT11"
  humidity:
    name: "Bed Room Humidity DHT11"
  update_interval: 60s
  model: DHT11

# SHT30传感器 - 高精度温湿度传感器
- platform: sht3xd
  temperature:
    name: "SHT30 Temperature"
    accuracy_decimals: 2
    filters:
      - offset: 0.0  # 温度校准偏移量
  humidity:
    name: "SHT30 Humidity"
    accuracy_decimals: 2
    filters:
      - offset: 0.0  # 湿度校准偏移量
  address: 0x44  # SHT30的I2C地址 (默认0x44，也可能是0x45)
  update_interval: 30s  # 更新间隔30秒

logger:
  baud_rate: 115200
ota:
  platform: esphome
api:

web_server:
  port: 80