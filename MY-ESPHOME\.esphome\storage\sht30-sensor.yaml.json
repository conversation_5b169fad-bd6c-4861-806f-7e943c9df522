{"storage_version": 1, "name": "sht30-uart-sensor", "friendly_name": null, "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "sht30-uart-sensor.local", "web_port": 80, "esp_platform": "ESP8266", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\sht30-uart-sensor", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\sht30-uart-sensor\\.pioenvs\\sht30-uart-sensor\\firmware.bin", "loaded_integrations": ["api", "async_tcp", "binary_sensor", "dht", "esp8266", "esphome", "globals", "interval", "json", "logger", "md5", "mdns", "network", "ota", "preferences", "restart", "safe_mode", "sensor", "socket", "status", "switch", "template", "text_sensor", "uart", "uptime", "web_server", "web_server_base", "wifi", "wifi_info", "wifi_signal"], "loaded_platforms": ["binary_sensor/status", "ota/esphome", "sensor/dht", "sensor/template", "sensor/uptime", "sensor/wifi_signal", "switch/restart", "text_sensor/wifi_info"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp8266"}