// Auto generated code by esphome
// ========== AUTO GENERATED INCLUDE BLOCK BEGIN ===========
#include "esphome.h"
using namespace esphome;
using std::isnan;
using std::min;
using std::max;
using namespace sensor;
using namespace text_sensor;
using namespace binary_sensor;
using namespace switch_;
logger::Logger *logger_logger_id;
web_server_base::WebServerBase *web_server_base_webserverbase_id;
wifi::WiFiComponent *wifi_wificomponent_id;
mdns::MDNSComponent *mdns_mdnscomponent_id;
esphome::ESPHomeOTAComponent *esphome_esphomeotacomponent_id;
safe_mode::SafeModeComponent *safe_mode_safemodecomponent_id;
api::APIServer *api_apiserver_id;
using namespace api;
web_server::WebServer *web_server_webserver_id;
const uint8_t ESPHOME_WEBSERVER_INDEX_HTML[174] PROGMEM = {60, 33, 68, 79, 67, 84, 89, 80, 69, 32, 104, 116, 109, 108, 62, 60, 104, 116, 109, 108, 62, 60, 104, 101, 97, 100, 62, 60, 109, 101, 116, 97, 32, 99, 104, 97, 114, 115, 101, 116, 61, 85, 84, 70, 45, 56, 62, 60, 108, 105, 110, 107, 32, 114, 101, 108, 61, 105, 99, 111, 110, 32, 104, 114, 101, 102, 61, 100, 97, 116, 97, 58, 62, 60, 47, 104, 101, 97, 100, 62, 60, 98, 111, 100, 121, 62, 60, 101, 115, 112, 45, 97, 112, 112, 62, 60, 47, 101, 115, 112, 45, 97, 112, 112, 62, 60, 115, 99, 114, 105, 112, 116, 32, 115, 114, 99, 61, 34, 104, 116, 116, 112, 115, 58, 47, 47, 111, 105, 46, 101, 115, 112, 104, 111, 109, 101, 46, 105, 111, 47, 118, 50, 47, 119, 119, 119, 46, 106, 115, 34, 62, 60, 47, 115, 99, 114, 105, 112, 116, 62, 60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62};
const size_t ESPHOME_WEBSERVER_INDEX_HTML_SIZE = 174;
StartupTrigger *startuptrigger_id;
Automation<> *automation_id;
DelayAction<> *delayaction_id;
LambdaAction<> *lambdaaction_id;
using namespace json;
preferences::IntervalSyncer *preferences_intervalsyncer_id;
using namespace uart;
uart::ESP8266UartComponent *uart_bus;
esphome::esp8266::ESP8266GPIOPin *esphome_esp8266_esp8266gpiopin_id;
esphome::esp8266::ESP8266GPIOPin *esphome_esp8266_esp8266gpiopin_id_2;
dht::DHT *dht_dht_id;
esphome::esp8266::ESP8266GPIOPin *esphome_esp8266_esp8266gpiopin_id_3;
sensor::Sensor *sensor_sensor_id;
sensor::Sensor *sensor_sensor_id_2;
template_::TemplateSensor *sht30_temp;
template_::TemplateSensor *sht30_hum;
wifi_signal::WiFiSignalSensor *wifi_signal_wifisignalsensor_id;
uptime::UptimeSecondsSensor *uptime_uptimesecondssensor_id;
interval::IntervalTrigger *interval_intervaltrigger_id;
Automation<> *automation_id_2;
wifi_info::SSIDWiFiInfo *wifi_info_ssidwifiinfo_id;
wifi_info::MacAddressWifiInfo *wifi_info_macaddresswifiinfo_id;
wifi_info::IPAddressWiFiInfo *wifi_info_ipaddresswifiinfo_id;
status::StatusBinarySensor *status_statusbinarysensor_id;
restart::RestartSwitch *restart_restartswitch_id;
globals::GlobalsComponent<std::string> *uart_buffer;
LambdaAction<> *lambdaaction_id_2;
interval::IntervalTrigger *interval_intervaltrigger_id_2;
Automation<> *automation_id_3;
uart::UARTWriteAction<> *uart_uartwriteaction_id;
const uint8_t ESPHOME_ESP8266_GPIO_INITIAL_MODE[16] = {INPUT_PULLUP, 255, 255, 255, INPUT, OUTPUT, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255};
const uint8_t ESPHOME_ESP8266_GPIO_INITIAL_LEVEL[16] = {255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255};
#define yield() esphome::yield()
#define millis() esphome::millis()
#define micros() esphome::micros()
#define delay(x) esphome::delay(x)
#define delayMicroseconds(x) esphome::delayMicroseconds(x)
// ========== AUTO GENERATED INCLUDE BLOCK END ==========="

void setup() {
  // ========== AUTO GENERATED CODE BEGIN ===========
  App.reserve_text_sensor(3);
  App.reserve_switch(1);
  App.reserve_sensor(6);
  App.reserve_binary_sensor(1);
  // esp8266:
  //   board: nodemcuv2
  //   framework:
  //     version: 3.1.2
  //     source: ~3.30102.0
  //     platform_version: platformio/espressif8266@4.2.1
  //   restore_from_flash: false
  //   early_pin_init: true
  //   board_flash_mode: dout
  esphome::esp8266::setup_preferences();
  // network:
  //   enable_ipv6: false
  //   min_ipv6_addr_count: 0
  // async_tcp:
  //   {}
  // esphome:
  //   name: sht30-uart-sensor
  //   on_boot:
  //     - priority: -100.0
  //       then:
  //         - delay: 3s
  //           type_id: delayaction_id
  //         - logger.log:
  //             format: SHT30 UART传感器设备启动完成
  //             level: DEBUG
  //             args: []
  //             tag: main
  //           type_id: lambdaaction_id
  //       automation_id: automation_id
  //       trigger_id: startuptrigger_id
  //   min_version: 2025.7.2
  //   build_path: build\sht30-uart-sensor
  //   friendly_name: ''
  //   platformio_options: {}
  //   includes: []
  //   libraries: []
  //   name_add_mac_suffix: false
  //   debug_scheduler: false
  //   areas: []
  //   devices: []
  App.pre_setup("sht30-uart-sensor", "", "", __DATE__ ", " __TIME__, false);
  App.reserve_components(25);
  // sensor:
  // text_sensor:
  // binary_sensor:
  // switch:
  // logger:
  //   baud_rate: 115200
  //   level: INFO
  //   id: logger_logger_id
  //   tx_buffer_size: 512
  //   deassert_rts_dtr: false
  //   hardware_uart: UART0
  //   logs: {}
  //   esp8266_store_log_strings_in_flash: true
  logger_logger_id = new logger::Logger(115200, 512);
  logger_logger_id->set_log_level(ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_uart_selection(logger::UART_SELECTION_UART0);
  logger_logger_id->pre_setup();
  logger_logger_id->set_component_source("logger");
  App.register_component(logger_logger_id);
  // web_server_base:
  //   id: web_server_base_webserverbase_id
  web_server_base_webserverbase_id = new web_server_base::WebServerBase();
  web_server_base_webserverbase_id->set_component_source("web_server_base");
  App.register_component(web_server_base_webserverbase_id);
  web_server_base::global_web_server_base = web_server_base_webserverbase_id;
  // wifi:
  //   ap:
  //     ssid: SHT30-UART Fallback Hotspot
  //     password: '12345678'
  //     id: wifi_wifiap_id
  //     ap_timeout: 1min
  //   id: wifi_wificomponent_id
  //   domain: .local
  //   reboot_timeout: 15min
  //   power_save_mode: NONE
  //   fast_connect: false
  //   output_power: 20.0
  //   passive_scan: false
  //   enable_on_boot: true
  //   networks:
  //     - ssid: !secret 'wifi_ssid'
  //       password: !secret 'wifi_password'
  //       id: wifi_wifiap_id_2
  //       priority: 0.0
  //   use_address: sht30-uart-sensor.local
  wifi_wificomponent_id = new wifi::WiFiComponent();
  wifi_wificomponent_id->set_use_address("sht30-uart-sensor.local");
  {
  wifi::WiFiAP wifi_wifiap_id_2 = wifi::WiFiAP();
  wifi_wifiap_id_2.set_ssid("HOME");
  wifi_wifiap_id_2.set_password("nb9d30@24zd");
  wifi_wifiap_id_2.set_priority(0.0f);
  wifi_wificomponent_id->add_sta(wifi_wifiap_id_2);
  }
  {
  wifi::WiFiAP wifi_wifiap_id = wifi::WiFiAP();
  wifi_wifiap_id.set_ssid("SHT30-UART Fallback Hotspot");
  wifi_wifiap_id.set_password("12345678");
  wifi_wificomponent_id->set_ap(wifi_wifiap_id);
  }
  wifi_wificomponent_id->set_ap_timeout(60000);
  wifi_wificomponent_id->set_reboot_timeout(900000);
  wifi_wificomponent_id->set_power_save_mode(wifi::WIFI_POWER_SAVE_NONE);
  wifi_wificomponent_id->set_fast_connect(false);
  wifi_wificomponent_id->set_passive_scan(false);
  wifi_wificomponent_id->set_output_power(20.0f);
  wifi_wificomponent_id->set_enable_on_boot(true);
  wifi_wificomponent_id->set_component_source("wifi");
  App.register_component(wifi_wificomponent_id);
  // mdns:
  //   id: mdns_mdnscomponent_id
  //   disabled: false
  //   services: []
  mdns_mdnscomponent_id = new mdns::MDNSComponent();
  mdns_mdnscomponent_id->set_component_source("mdns");
  App.register_component(mdns_mdnscomponent_id);
  // ota:
  // ota.esphome:
  //   platform: esphome
  //   id: esphome_esphomeotacomponent_id
  //   version: 2
  //   port: 8266
  esphome_esphomeotacomponent_id = new esphome::ESPHomeOTAComponent();
  esphome_esphomeotacomponent_id->set_port(8266);
  esphome_esphomeotacomponent_id->set_component_source("esphome.ota");
  App.register_component(esphome_esphomeotacomponent_id);
  // safe_mode:
  //   id: safe_mode_safemodecomponent_id
  //   boot_is_good_after: 1min
  //   disabled: false
  //   num_attempts: 10
  //   reboot_timeout: 5min
  safe_mode_safemodecomponent_id = new safe_mode::SafeModeComponent();
  safe_mode_safemodecomponent_id->set_component_source("safe_mode");
  App.register_component(safe_mode_safemodecomponent_id);
  if (safe_mode_safemodecomponent_id->should_enter_safe_mode(10, 300000, 60000)) return;
  // api:
  //   id: api_apiserver_id
  //   port: 6053
  //   password: ''
  //   reboot_timeout: 15min
  //   batch_delay: 100ms
  //   custom_services: false
  api_apiserver_id = new api::APIServer();
  api_apiserver_id->set_component_source("api");
  App.register_component(api_apiserver_id);
  api_apiserver_id->set_port(6053);
  api_apiserver_id->set_reboot_timeout(900000);
  api_apiserver_id->set_batch_delay(100);
  // web_server:
  //   port: 80
  //   auth:
  //     username: admin
  //     password: !secret 'wifi_password'
  //   id: web_server_webserver_id
  //   version: 2
  //   enable_private_network_access: true
  //   web_server_base_id: web_server_base_webserverbase_id
  //   include_internal: false
  //   log: true
  //   css_url: ''
  //   js_url: https:oi.esphome.io/v2/www.js
  web_server_webserver_id = new web_server::WebServer(web_server_base_webserverbase_id);
  web_server_webserver_id->set_component_source("web_server");
  App.register_component(web_server_webserver_id);
  web_server_base_webserverbase_id->set_port(80);
  web_server_webserver_id->set_expose_log(true);
  web_server_base_webserverbase_id->set_auth_username("admin");
  web_server_base_webserverbase_id->set_auth_password("nb9d30@24zd");
  web_server_webserver_id->set_include_internal(false);
  startuptrigger_id = new StartupTrigger(-100.0f);
  startuptrigger_id->set_component_source("esphome.coroutine");
  App.register_component(startuptrigger_id);
  automation_id = new Automation<>(startuptrigger_id);
  delayaction_id = new DelayAction<>();
  delayaction_id->set_component_source("esphome.coroutine");
  App.register_component(delayaction_id);
  delayaction_id->set_delay(3000);
  lambdaaction_id = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "SHT30 UART\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207\345\220\257\345\212\250\345\256\214\346\210\220");
  });
  automation_id->add_actions({delayaction_id, lambdaaction_id});
  // json:
  //   {}
  // preferences:
  //   id: preferences_intervalsyncer_id
  //   flash_write_interval: 60s
  preferences_intervalsyncer_id = new preferences::IntervalSyncer();
  preferences_intervalsyncer_id->set_write_interval(60000);
  preferences_intervalsyncer_id->set_component_source("preferences");
  App.register_component(preferences_intervalsyncer_id);
  // uart:
  //   id: uart_bus
  //   tx_pin:
  //     number: 5
  //     mode:
  //       output: true
  //       input: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //       analog: false
  //     id: esphome_esp8266_esp8266gpiopin_id
  //     inverted: false
  //   rx_pin:
  //     number: 4
  //     mode:
  //       input: true
  //       output: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //       analog: false
  //     id: esphome_esp8266_esp8266gpiopin_id_2
  //     inverted: false
  //   baud_rate: 9600
  //   data_bits: 8
  //   parity: NONE
  //   stop_bits: 1
  //   rx_buffer_size: 256
  uart_bus = new uart::ESP8266UartComponent();
  uart_bus->set_component_source("uart");
  App.register_component(uart_bus);
  uart_bus->set_baud_rate(9600);
  esphome_esp8266_esp8266gpiopin_id = new esphome::esp8266::ESP8266GPIOPin();
  esphome_esp8266_esp8266gpiopin_id->set_pin(5);
  esphome_esp8266_esp8266gpiopin_id->set_inverted(false);
  esphome_esp8266_esp8266gpiopin_id->set_flags(gpio::Flags::FLAG_OUTPUT);
  uart_bus->set_tx_pin(esphome_esp8266_esp8266gpiopin_id);
  esphome_esp8266_esp8266gpiopin_id_2 = new esphome::esp8266::ESP8266GPIOPin();
  esphome_esp8266_esp8266gpiopin_id_2->set_pin(4);
  esphome_esp8266_esp8266gpiopin_id_2->set_inverted(false);
  esphome_esp8266_esp8266gpiopin_id_2->set_flags(gpio::Flags::FLAG_INPUT);
  uart_bus->set_rx_pin(esphome_esp8266_esp8266gpiopin_id_2);
  uart_bus->set_rx_buffer_size(256);
  uart_bus->set_stop_bits(1);
  uart_bus->set_data_bits(8);
  uart_bus->set_parity(uart::UART_CONFIG_PARITY_NONE);
  // sensor.dht:
  //   platform: dht
  //   pin:
  //     number: 0
  //     mode:
  //       input: true
  //       pullup: true
  //       output: false
  //       open_drain: false
  //       pulldown: false
  //       analog: false
  //     id: esphome_esp8266_esp8266gpiopin_id_3
  //     inverted: false
  //   temperature:
  //     name: Bed Room Temperature DHT11
  //     disabled_by_default: false
  //     id: sensor_sensor_id
  //     force_update: false
  //     unit_of_measurement: °C
  //     accuracy_decimals: 1
  //     device_class: temperature
  //     state_class: measurement
  //   humidity:
  //     name: Bed Room Humidity DHT11
  //     disabled_by_default: false
  //     id: sensor_sensor_id_2
  //     force_update: false
  //     unit_of_measurement: '%'
  //     accuracy_decimals: 0
  //     device_class: humidity
  //     state_class: measurement
  //   update_interval: 60s
  //   model: DHT11
  //   id: dht_dht_id
  dht_dht_id = new dht::DHT();
  dht_dht_id->set_update_interval(60000);
  dht_dht_id->set_component_source("dht.sensor");
  App.register_component(dht_dht_id);
  esphome_esp8266_esp8266gpiopin_id_3 = new esphome::esp8266::ESP8266GPIOPin();
  esphome_esp8266_esp8266gpiopin_id_3->set_pin(0);
  esphome_esp8266_esp8266gpiopin_id_3->set_inverted(false);
  esphome_esp8266_esp8266gpiopin_id_3->set_flags((gpio::Flags::FLAG_INPUT | gpio::Flags::FLAG_PULLUP));
  dht_dht_id->set_pin(esphome_esp8266_esp8266gpiopin_id_3);
  sensor_sensor_id = new sensor::Sensor();
  App.register_sensor(sensor_sensor_id);
  sensor_sensor_id->set_name("Bed Room Temperature DHT11");
  sensor_sensor_id->set_object_id("bed_room_temperature_dht11");
  sensor_sensor_id->set_disabled_by_default(false);
  sensor_sensor_id->set_device_class("temperature");
  sensor_sensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  sensor_sensor_id->set_unit_of_measurement("\302\260C");
  sensor_sensor_id->set_accuracy_decimals(1);
  sensor_sensor_id->set_force_update(false);
  dht_dht_id->set_temperature_sensor(sensor_sensor_id);
  sensor_sensor_id_2 = new sensor::Sensor();
  App.register_sensor(sensor_sensor_id_2);
  sensor_sensor_id_2->set_name("Bed Room Humidity DHT11");
  sensor_sensor_id_2->set_object_id("bed_room_humidity_dht11");
  sensor_sensor_id_2->set_disabled_by_default(false);
  sensor_sensor_id_2->set_device_class("humidity");
  sensor_sensor_id_2->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  sensor_sensor_id_2->set_unit_of_measurement("%");
  sensor_sensor_id_2->set_accuracy_decimals(0);
  sensor_sensor_id_2->set_force_update(false);
  dht_dht_id->set_humidity_sensor(sensor_sensor_id_2);
  dht_dht_id->set_dht_model(dht::DHT_MODEL_DHT11);
  // sensor.template:
  //   platform: template
  //   name: SHT30 UART Temperature
  //   id: sht30_temp
  //   unit_of_measurement: °C
  //   device_class: temperature
  //   state_class: measurement
  //   accuracy_decimals: 1
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  sht30_temp = new template_::TemplateSensor();
  App.register_sensor(sht30_temp);
  sht30_temp->set_name("SHT30 UART Temperature");
  sht30_temp->set_object_id("sht30_uart_temperature");
  sht30_temp->set_disabled_by_default(false);
  sht30_temp->set_device_class("temperature");
  sht30_temp->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  sht30_temp->set_unit_of_measurement("\302\260C");
  sht30_temp->set_accuracy_decimals(1);
  sht30_temp->set_force_update(false);
  sht30_temp->set_update_interval(60000);
  sht30_temp->set_component_source("template.sensor");
  App.register_component(sht30_temp);
  // sensor.template:
  //   platform: template
  //   name: SHT30 UART Humidity
  //   id: sht30_hum
  //   unit_of_measurement: '%'
  //   device_class: humidity
  //   state_class: measurement
  //   accuracy_decimals: 1
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  sht30_hum = new template_::TemplateSensor();
  App.register_sensor(sht30_hum);
  sht30_hum->set_name("SHT30 UART Humidity");
  sht30_hum->set_object_id("sht30_uart_humidity");
  sht30_hum->set_disabled_by_default(false);
  sht30_hum->set_device_class("humidity");
  sht30_hum->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  sht30_hum->set_unit_of_measurement("%");
  sht30_hum->set_accuracy_decimals(1);
  sht30_hum->set_force_update(false);
  sht30_hum->set_update_interval(60000);
  sht30_hum->set_component_source("template.sensor");
  App.register_component(sht30_hum);
  // sensor.wifi_signal:
  //   platform: wifi_signal
  //   name: WiFi Signal
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: wifi_signal_wifisignalsensor_id
  //   unit_of_measurement: dBm
  //   accuracy_decimals: 0
  //   device_class: signal_strength
  //   state_class: measurement
  //   entity_category: diagnostic
  wifi_signal_wifisignalsensor_id = new wifi_signal::WiFiSignalSensor();
  App.register_sensor(wifi_signal_wifisignalsensor_id);
  wifi_signal_wifisignalsensor_id->set_name("WiFi Signal");
  wifi_signal_wifisignalsensor_id->set_object_id("wifi_signal");
  wifi_signal_wifisignalsensor_id->set_disabled_by_default(false);
  wifi_signal_wifisignalsensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_signal_wifisignalsensor_id->set_device_class("signal_strength");
  wifi_signal_wifisignalsensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  wifi_signal_wifisignalsensor_id->set_unit_of_measurement("dBm");
  wifi_signal_wifisignalsensor_id->set_accuracy_decimals(0);
  wifi_signal_wifisignalsensor_id->set_force_update(false);
  wifi_signal_wifisignalsensor_id->set_update_interval(60000);
  wifi_signal_wifisignalsensor_id->set_component_source("wifi_signal.sensor");
  App.register_component(wifi_signal_wifisignalsensor_id);
  // sensor.uptime:
  //   platform: uptime
  //   name: Uptime
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: uptime_uptimesecondssensor_id
  //   unit_of_measurement: s
  //   icon: mdi:timer-outline
  //   accuracy_decimals: 0
  //   device_class: duration
  //   state_class: total_increasing
  //   entity_category: diagnostic
  //   type: seconds
  uptime_uptimesecondssensor_id = new uptime::UptimeSecondsSensor();
  App.register_sensor(uptime_uptimesecondssensor_id);
  uptime_uptimesecondssensor_id->set_name("Uptime");
  uptime_uptimesecondssensor_id->set_object_id("uptime");
  uptime_uptimesecondssensor_id->set_disabled_by_default(false);
  uptime_uptimesecondssensor_id->set_icon("mdi:timer-outline");
  uptime_uptimesecondssensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  uptime_uptimesecondssensor_id->set_device_class("duration");
  uptime_uptimesecondssensor_id->set_state_class(sensor::STATE_CLASS_TOTAL_INCREASING);
  uptime_uptimesecondssensor_id->set_unit_of_measurement("s");
  uptime_uptimesecondssensor_id->set_accuracy_decimals(0);
  uptime_uptimesecondssensor_id->set_force_update(false);
  uptime_uptimesecondssensor_id->set_update_interval(60000);
  uptime_uptimesecondssensor_id->set_component_source("uptime.sensor");
  App.register_component(uptime_uptimesecondssensor_id);
  // interval:
  //   - interval: 100ms
  //     then:
  //       - lambda: !lambda |-
  //            读取UART数据
  //           uint8_t data[256];
  //           size_t len = id(uart_bus).read_array(data, sizeof(data));
  //           if (len > 0) {
  //             for (size_t i = 0; i < len; i++) {
  //               char c = (char)data[i];
  //               if (c == '\n' || c == '\r') {
  //                  处理完整的一行数据
  //                 if (!id(uart_buffer).empty()) {
  //                   std::string line = id(uart_buffer);
  //                   ESP_LOGD("sht30_uart", "收到数据: %s", line.c_str());
  //   
  //                    解析数据格式：R:070.0RH 032.4C
  //                   if (line.rfind("R:", 0) == 0 && line.length() >= 17) {
  //                      避免使用异常处理，改用手动解析
  //                     bool parse_ok = true;
  //                     float hum = 0.0, temp = 0.0;
  //   
  //                      解析湿度 (位置2-6: "070.0")
  //                     std::string hum_str = line.substr(2, 5);
  //                     char* hum_end;
  //                     hum = strtof(hum_str.c_str(), &hum_end);
  //                     if (hum_end == hum_str.c_str()) parse_ok = false;
  //   
  //                      解析温度 (位置11-15: "032.4")
  //                     if (parse_ok && line.length() >= 16) {
  //                       std::string temp_str = line.substr(11, 5);
  //                       char* temp_end;
  //                       temp = strtof(temp_str.c_str(), &temp_end);
  //                       if (temp_end == temp_str.c_str()) parse_ok = false;
  //                     }
  //   
  //                     if (parse_ok) {
  //                       ESP_LOGI("sht30_uart", "解析成功 - 湿度: %.1f%%, 温度: %.1f°C", hum, temp);
  //   
  //                        更新传感器状态
  //                       id(sht30_temp).publish_state(temp);
  //                       id(sht30_hum).publish_state(hum);
  //                     } else {
  //                       ESP_LOGW("sht30_uart", "解析失败: %s", line.c_str());
  //                     }
  //                   } else {
  //                     ESP_LOGD("sht30_uart", "数据格式不匹配: %s", line.c_str());
  //                   }
  //                   id(uart_buffer) = "";   清空缓冲区
  //                 }
  //               } else {
  //                 id(uart_buffer) += c;
  //                  防止缓冲区过长
  //                 if (id(uart_buffer).length() > 100) {
  //                   id(uart_buffer) = "";
  //                 }
  //               }
  //             }
  //           }
  //         type_id: lambdaaction_id_2
  //     trigger_id: trigger_id
  //     automation_id: automation_id_2
  //     id: interval_intervaltrigger_id
  //     startup_delay: 0s
  //   - interval: 5s
  //     then:
  //       - uart.write:
  //           id: uart_bus
  //           data: !!binary |
  //             UkVBRA0K
  //         type_id: uart_uartwriteaction_id
  //     trigger_id: trigger_id_2
  //     automation_id: automation_id_3
  //     id: interval_intervaltrigger_id_2
  //     startup_delay: 0s
  interval_intervaltrigger_id = new interval::IntervalTrigger();
  interval_intervaltrigger_id->set_component_source("interval");
  App.register_component(interval_intervaltrigger_id);
  automation_id_2 = new Automation<>(interval_intervaltrigger_id);
  // text_sensor.wifi_info:
  //   platform: wifi_info
  //   ip_address:
  //     name: IP Address
  //     disabled_by_default: false
  //     id: wifi_info_ipaddresswifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   ssid:
  //     name: Connected SSID
  //     disabled_by_default: false
  //     id: wifi_info_ssidwifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   mac_address:
  //     name: Mac Address
  //     disabled_by_default: false
  //     id: wifi_info_macaddresswifiinfo_id
  //     entity_category: diagnostic
  wifi_info_ssidwifiinfo_id = new wifi_info::SSIDWiFiInfo();
  App.register_text_sensor(wifi_info_ssidwifiinfo_id);
  wifi_info_ssidwifiinfo_id->set_name("Connected SSID");
  wifi_info_ssidwifiinfo_id->set_object_id("connected_ssid");
  wifi_info_ssidwifiinfo_id->set_disabled_by_default(false);
  wifi_info_ssidwifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ssidwifiinfo_id->set_update_interval(1000);
  wifi_info_ssidwifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ssidwifiinfo_id);
  wifi_info_macaddresswifiinfo_id = new wifi_info::MacAddressWifiInfo();
  App.register_text_sensor(wifi_info_macaddresswifiinfo_id);
  wifi_info_macaddresswifiinfo_id->set_name("Mac Address");
  wifi_info_macaddresswifiinfo_id->set_object_id("mac_address");
  wifi_info_macaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_macaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_macaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_macaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id = new wifi_info::IPAddressWiFiInfo();
  App.register_text_sensor(wifi_info_ipaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id->set_name("IP Address");
  wifi_info_ipaddresswifiinfo_id->set_object_id("ip_address");
  wifi_info_ipaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_ipaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ipaddresswifiinfo_id->set_update_interval(1000);
  wifi_info_ipaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ipaddresswifiinfo_id);
  // binary_sensor.status:
  //   platform: status
  //   name: Status
  //   disabled_by_default: false
  //   id: status_statusbinarysensor_id
  //   entity_category: diagnostic
  //   device_class: connectivity
  status_statusbinarysensor_id = new status::StatusBinarySensor();
  App.register_binary_sensor(status_statusbinarysensor_id);
  status_statusbinarysensor_id->set_name("Status");
  status_statusbinarysensor_id->set_object_id("status");
  status_statusbinarysensor_id->set_disabled_by_default(false);
  status_statusbinarysensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  status_statusbinarysensor_id->set_device_class("connectivity");
  status_statusbinarysensor_id->set_trigger_on_initial_state(false);
  status_statusbinarysensor_id->set_component_source("status.binary_sensor");
  App.register_component(status_statusbinarysensor_id);
  // switch.restart:
  //   platform: restart
  //   name: Restart
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   id: restart_restartswitch_id
  //   entity_category: config
  //   icon: mdi:restart
  restart_restartswitch_id = new restart::RestartSwitch();
  App.register_switch(restart_restartswitch_id);
  restart_restartswitch_id->set_name("Restart");
  restart_restartswitch_id->set_object_id("restart");
  restart_restartswitch_id->set_disabled_by_default(false);
  restart_restartswitch_id->set_icon("mdi:restart");
  restart_restartswitch_id->set_entity_category(::ENTITY_CATEGORY_CONFIG);
  restart_restartswitch_id->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  restart_restartswitch_id->set_component_source("restart.switch");
  App.register_component(restart_restartswitch_id);
  // md5:
  // socket:
  //   implementation: lwip_tcp
  // globals:
  //   id: uart_buffer
  //   type: std::string
  //   restore_value: false
  //   initial_value: '""'
  uart_buffer = new globals::GlobalsComponent<std::string>("");
  uart_buffer->set_component_source("globals");
  App.register_component(uart_buffer);
  lambdaaction_id_2 = new LambdaAction<>([=]() -> void {
      #line 88 "sht30-sensor.yaml"
       
      uint8_t data[256];
      size_t len = uart_bus->read_array(data, sizeof(data));
      if (len > 0) {
        for (size_t i = 0; i < len; i++) {
          char c = (char)data[i];
          if (c == '\n' || c == '\r') {
             
            if (!uart_buffer->value().empty()) {
              std::string line = uart_buffer->value();
              ESP_LOGD("sht30_uart", "收到数据: %s", line.c_str());
      
               
              if (line.rfind("R:", 0) == 0 && line.length() >= 17) {
                 
                bool parse_ok = true;
                float hum = 0.0, temp = 0.0;
      
                 
                std::string hum_str = line.substr(2, 5);
                char* hum_end;
                hum = strtof(hum_str.c_str(), &hum_end);
                if (hum_end == hum_str.c_str()) parse_ok = false;
      
                 
                if (parse_ok && line.length() >= 16) {
                  std::string temp_str = line.substr(11, 5);
                  char* temp_end;
                  temp = strtof(temp_str.c_str(), &temp_end);
                  if (temp_end == temp_str.c_str()) parse_ok = false;
                }
      
                if (parse_ok) {
                  ESP_LOGI("sht30_uart", "解析成功 - 湿度: %.1f%%, 温度: %.1f°C", hum, temp);
      
                   
                  sht30_temp->publish_state(temp);
                  sht30_hum->publish_state(hum);
                } else {
                  ESP_LOGW("sht30_uart", "解析失败: %s", line.c_str());
                }
              } else {
                ESP_LOGD("sht30_uart", "数据格式不匹配: %s", line.c_str());
              }
              uart_buffer->value() = "";   
            }
          } else {
            uart_buffer->value() += c;
             
            if (uart_buffer->value().length() > 100) {
              uart_buffer->value() = "";
            }
          }
        }
      }
  });
  automation_id_2->add_actions({lambdaaction_id_2});
  interval_intervaltrigger_id->set_update_interval(100);
  interval_intervaltrigger_id->set_startup_delay(0);
  interval_intervaltrigger_id_2 = new interval::IntervalTrigger();
  interval_intervaltrigger_id_2->set_component_source("interval");
  App.register_component(interval_intervaltrigger_id_2);
  automation_id_3 = new Automation<>(interval_intervaltrigger_id_2);
  uart_uartwriteaction_id = new uart::UARTWriteAction<>();
  uart_uartwriteaction_id->set_parent(uart_bus);
  uart_uartwriteaction_id->set_data_static({82, 69, 65, 68, 13, 10});
  automation_id_3->add_actions({uart_uartwriteaction_id});
  interval_intervaltrigger_id_2->set_update_interval(5000);
  interval_intervaltrigger_id_2->set_startup_delay(0);
  // =========== AUTO GENERATED CODE END ============
  App.setup();
}

void loop() {
  App.loop();
}
