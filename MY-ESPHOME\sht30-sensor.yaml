esphome:
  name: sht30-uart-sensor
  on_boot:
    priority: -100
    then:
      - delay: 3s
      - logger.log: "SHT30 UART传感器设备启动完成"

esp8266:
  board: nodemcuv2

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # 可选：设置静态IP（如果需要）
  # manual_ip:
  #   static_ip: *************
  #   gateway: ***********
  #   subnet: *************

  # WiFi连接失败时的处理
  ap:
    ssid: "SHT30-UART Fallback Hotspot"
    password: "12345678"

# UART配置 - 用于读取SHT30传感器数据
uart:
  id: uart_bus
  tx_pin: D1  # TX引脚 (GPIO5) - 如果需要发送命令到传感器
  rx_pin: D2  # RX引脚 (GPIO4) - 接收传感器数据
  baud_rate: 9600  # 波特率，根据您的传感器模块调整
  data_bits: 8
  parity: NONE
  stop_bits: 1

# 全局变量存储UART数据缓冲区
globals:
  - id: uart_buffer
    type: std::string
    restore_value: no
    initial_value: '""'

sensor:
  # DHT11传感器 (保留原有配置)
  - platform: dht
    pin: D3  # DHT11使用D3引脚
    temperature:
      name: "Bed Room Temperature DHT11"
    humidity:
      name: "Bed Room Humidity DHT11"
    update_interval: 60s
    model: DHT11
  # SHT30 UART传感器 - 温度
  - platform: template
    name: "SHT30 UART Temperature"
    id: sht30_temp
    unit_of_measurement: "°C"
    device_class: temperature
    state_class: measurement
    accuracy_decimals: 1

  # SHT30 UART传感器 - 湿度
  - platform: template
    name: "SHT30 UART Humidity"
    id: sht30_hum
    unit_of_measurement: "%"
    device_class: humidity
    state_class: measurement
    accuracy_decimals: 1

  # WiFi信号强度传感器
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 设备运行时间传感器
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

# UART数据处理间隔
interval:
  - interval: 100ms  # 每100ms检查一次UART数据
    then:
      - lambda: |-
          // 读取UART数据
          std::vector<uint8_t> data;
          if (id(uart_bus).read_array(data)) {
            for (uint8_t byte : data) {
              char c = (char)byte;
              if (c == '\n' || c == '\r') {
                // 处理完整的一行数据
                if (!id(uart_buffer).empty()) {
                  std::string line = id(uart_buffer);
                  ESP_LOGD("sht30_uart", "收到数据: %s", line.c_str());

                  // 解析数据格式：R:070.0RH 032.4C
                  if (line.rfind("R:", 0) == 0 && line.length() >= 17) {
                    // 避免使用异常处理，改用手动解析
                    bool parse_ok = true;
                    float hum = 0.0, temp = 0.0;

                    // 解析湿度 (位置2-6: "070.0")
                    std::string hum_str = line.substr(2, 5);
                    char* hum_end;
                    hum = strtof(hum_str.c_str(), &hum_end);
                    if (hum_end == hum_str.c_str()) parse_ok = false;

                    // 解析温度 (位置11-15: "032.4")
                    if (parse_ok && line.length() >= 16) {
                      std::string temp_str = line.substr(11, 5);
                      char* temp_end;
                      temp = strtof(temp_str.c_str(), &temp_end);
                      if (temp_end == temp_str.c_str()) parse_ok = false;
                    }

                    if (parse_ok) {
                      ESP_LOGI("sht30_uart", "解析成功 - 湿度: %.1f%%, 温度: %.1f°C", hum, temp);

                      // 更新传感器状态
                      id(sht30_temp).publish_state(temp);
                      id(sht30_hum).publish_state(hum);
                    } else {
                      ESP_LOGW("sht30_uart", "解析失败: %s", line.c_str());
                    }
                  } else {
                    ESP_LOGD("sht30_uart", "数据格式不匹配: %s", line.c_str());
                  }
                  id(uart_buffer) = "";  // 清空缓冲区
                }
              } else {
                id(uart_buffer) += c;
                // 防止缓冲区过长
                if (id(uart_buffer).length() > 100) {
                  id(uart_buffer) = "";
                }
              }
            }
          }

  # 可选：定期发送读取命令到传感器
  - interval: 5s
    then:
      - uart.write:
          id: uart_bus
          data: "READ\r\n"  # 发送读取命令到传感器（如果传感器需要命令触发）
      
# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

# 日志配置
logger:
  baud_rate: 115200
  level: INFO

# OTA更新
ota:
  platform: esphome
  # password: !secret ota_password  # 可以从secrets.yaml读取OTA密码

# API配置
api:
  # encryption:
  #   key: !secret api_encryption_key  # 可以从secrets.yaml读取API密钥

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password  # 使用WiFi密码作为web界面密码
