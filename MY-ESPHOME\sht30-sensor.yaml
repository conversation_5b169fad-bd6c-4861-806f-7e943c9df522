esphome:
  name: sht30-sensor
  on_boot:
    priority: -100
    then:
      - delay: 3s
      - logger.log: "SHT30传感器设备启动完成"

esp8266:
  board: nodemcuv2

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # 可选：设置静态IP（如果需要）
  # manual_ip:
  #   static_ip: *************
  #   gateway: ***********
  #   subnet: *************
  
  # WiFi连接失败时的处理
  ap:
    ssid: "SHT30-Sensor Fallback Hotspot"
    password: "12345678"

esphome:
  name: sht30_uart
  includes:
    - sht30_uart.h

uart:
  id: uart_bus
  tx_pin: GPIO1  # 根据你实际接线调整
  rx_pin: GPIO3
  baud_rate: 9600

sensor:
  - platform: template
    name: "SHT30 Temperature"
    id: sht30_temp
    unit_of_measurement: "°C"
    device_class: temperature
    state_class: measurement
    accuracy_decimals: 1

  - platform: template
    name: "SHT30 Humidity"
    id: sht30_hum
    unit_of_measurement: "%"
    device_class: humidity
    state_class: measurement
    accuracy_decimals: 1

    
  # WiFi信号强度传感器
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 设备运行时间传感器
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

custom_component:
  - lambda: |-
      auto my_sht30 = new SHT30UART(id(uart_bus), id(sht30_temp), id(sht30_hum));
      return {my_sht30};
      
# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

# 日志配置
logger:
  baud_rate: 115200
  level: INFO

# OTA更新
ota:
  platform: esphome
  # password: !secret ota_password  # 可以从secrets.yaml读取OTA密码

# API配置
api:
  # encryption:
  #   key: !secret api_encryption_key  # 可以从secrets.yaml读取API密钥

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password  # 使用WiFi密码作为web界面密码
