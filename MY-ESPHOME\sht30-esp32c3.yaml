esphome:
  name: myesp32c3
  friendly_name: "SHT30 ESP32-C3 Sensor"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # WiFi连接失败时的处理
  ap:
    ssid: "ESP32C3-SHT30 Fallback Hotspot"
    password: "12345678"

  # WiFi连接状态回调
  on_connect:
    - logger.log: "WiFi连接成功！"
  on_disconnect:
    - logger.log: "WiFi连接断开！"

# UART配置 - 用于读取SHT30传感器数据
uart:
  id: uart_bus
  tx_pin: GPIO0  # TX引脚 - 如果需要发送命令到传感器
  rx_pin: GPIO1  # RX引脚 - 接收传感器数据
  baud_rate: 115200  # 尝试更高的波特率
  data_bits: 8
  parity: NONE
  stop_bits: 1
  rx_buffer_size: 512  # 增加接收缓冲区大小
  debug:
    direction: RX  # 只调试接收数据
    dummy_receiver: false

# 全局变量存储UART数据缓冲区
globals:
  - id: uart_buffer
    type: std::string
    restore_value: no
    initial_value: '""'

sensor:
  # SHT30 UART传感器 - 温度
  - platform: template
    name: "SHT30 UART Temperature"
    id: sht30_temp
    unit_of_measurement: "°C"
    device_class: temperature
    state_class: measurement
    accuracy_decimals: 1

  # SHT30 UART传感器 - 湿度
  - platform: template
    name: "SHT30 UART Humidity"
    id: sht30_hum
    unit_of_measurement: "%"
    device_class: humidity
    state_class: measurement
    accuracy_decimals: 1

  # WiFi信号强度传感器
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 设备运行时间传感器
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

  # ESP32-C3内部温度传感器
  - platform: internal_temperature
    name: "ESP32-C3 Internal Temperature"
    update_interval: 60s

# UART数据处理间隔
interval:
  - interval: 100ms  # 每100ms检查一次UART数据
    then:
      - lambda: |-
          // 读取UART数据
          uint8_t data[256];
          size_t len = id(uart_bus).read_array(data, sizeof(data));
          
          ESP_LOGD("sht30_uart", "UART读取到 %d 字节数据", len);
          
          if (len > 0) {
            // 打印原始字节数据（十六进制）
            std::string hex_data = "";
            for (size_t i = 0; i < len; i++) {
              char hex_char[4];
              sprintf(hex_char, "%02X ", data[i]);
              hex_data += hex_char;
            }
            ESP_LOGD("sht30_uart", "原始数据(HEX): %s", hex_data.c_str());
            
            // 处理字符数据
            for (size_t i = 0; i < len; i++) {
              char c = (char)data[i];
              
              // 打印可见字符
              if (c >= 32 && c <= 126) {
                ESP_LOGD("sht30_uart", "收到字符: '%c' (0x%02X)", c, (uint8_t)c);
              } else {
                ESP_LOGD("sht30_uart", "收到控制字符: 0x%02X", (uint8_t)c);
              }
              
              if (c == '\n' || c == '\r') {
                // 处理完整的一行数据
                if (!id(uart_buffer).empty()) {
                  std::string line = id(uart_buffer);
                  ESP_LOGI("sht30_uart", "完整行数据: '%s' (长度: %d)", line.c_str(), line.length());
                  
                  // 解析数据格式：R:070.0RH 032.4C
                  if (line.rfind("R:", 0) == 0 && line.length() >= 17) {
                    // 避免使用异常处理，改用手动解析
                    bool parse_ok = true;
                    float hum = 0.0, temp = 0.0;
                    
                    // 解析湿度 (位置2-6: "070.0")
                    std::string hum_str = line.substr(2, 5);
                    char* hum_end;
                    hum = strtof(hum_str.c_str(), &hum_end);
                    if (hum_end == hum_str.c_str()) parse_ok = false;
                    
                    // 解析温度 (位置11-15: "032.4")
                    if (parse_ok && line.length() >= 16) {
                      std::string temp_str = line.substr(11, 5);
                      char* temp_end;
                      temp = strtof(temp_str.c_str(), &temp_end);
                      if (temp_end == temp_str.c_str()) parse_ok = false;
                    }
                    
                    if (parse_ok) {
                      ESP_LOGI("sht30_uart", "解析成功 - 湿度: %.1f%%, 温度: %.1f°C", hum, temp);
                      
                      // 更新传感器状态
                      id(sht30_temp).publish_state(temp);
                      id(sht30_hum).publish_state(hum);
                    } else {
                      ESP_LOGW("sht30_uart", "解析失败: %s", line.c_str());
                    }
                  } else {
                    ESP_LOGD("sht30_uart", "数据格式不匹配: %s", line.c_str());
                  }
                  
                  id(uart_buffer) = "";  // 清空缓冲区
                }
              } else {
                id(uart_buffer) += c;
                // 防止缓冲区过长
                if (id(uart_buffer).length() > 200) {
                  ESP_LOGW("sht30_uart", "缓冲区过长，清空: %s", id(uart_buffer).c_str());
                  id(uart_buffer) = "";
                }
              }
            }
          } else {
            ESP_LOGD("sht30_uart", "UART无数据");
          }

  # 可选：定期发送读取命令到传感器
  - interval: 5s
    then:
      - uart.write:
          id: uart_bus
          data: "READ\r\n"  # 发送读取命令到传感器（如果传感器需要命令触发）

# 状态LED指示
light:
  - platform: status_led
    name: "Status LED"
    pin: GPIO8  # ESP32-C3开发板上的LED引脚

# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

# 日志配置
logger:
  baud_rate: 115200
  level: DEBUG  # 改为DEBUG级别以查看详细信息
  logs:
    sht30_uart: DEBUG
    uart_debug: INFO
    uart: DEBUG

# OTA更新
ota:
  platform: esphome

# API配置
api:

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password
