; Auto generated code by esphome

[common]
lib_deps =
build_flags =
upload_flags =

; ========== AUTO GENERATED CODE BEGIN ===========
[platformio]
description = ESPHome 2025.7.2
[env:sht30-uart-sensor]
board = nodemcuv2
board_build.flash_mode = dout
board_build.ldscript = eagle.flash.4m.ld
build_flags =
    -DESPHOME_LOG_LEVEL=ESPHOME_LOG_LEVEL_INFO
    -DNEW_OOM_ABORT
    -DPIO_FRAMEWORK_ARDUINO_LWIP2_HIGHER_BANDWIDTH_LOW_FLASH
    -DUSE_ARDUINO
    -DUSE_ESP8266
    -DUSE_ESP8266_FRAMEWORK_ARDUINO
    -DUSE_STORE_LOG_STR_IN_FLASH
    -Wno-nonnull-compare
    -Wno-sign-compare
    -Wno-unused-but-set-variable
    -Wno-unused-variable
    -fno-exceptions
    -std=gnu++20
build_unflags =
    -std=gnu++11
    -std=gnu++14
    -std=gnu++17
    -std=gnu++23
    -std=gnu++2a
    -std=gnu++2b
    -std=gnu++2c
extra_scripts =
    post:post_build.py
    pre:cxx_flags.py
framework = arduino
lib_compat_mode = strict
lib_deps =
    ESP32Async/ESPAsyncTCP@2.0.0
    ESP8266WiFi
    ESP32Async/ESPAsyncWebServer@3.7.10
    ESP8266mDNS
    bblanchon/ArduinoJson@7.4.2
    ${common.lib_deps}
lib_ldf_mode = off
platform = platformio/espressif8266@4.2.1
platform_packages =
    platformio/framework-arduinoespressif8266@~3.30102.0
; =========== AUTO GENERATED CODE END ============

