#include "esphome.h"
void process_line(const std::string &line) {
  // 期望格式：R:070.0RH 032.4C
  if (line.rfind("R:", 0) == 0 && line.length() >= 17) {
    try {
      float hum = std::stof(line.substr(2, 5));   // 070.0
      float temp = std::stof(line.substr(11, 5)); // 032.4

      humidity_sensor->publish_state(hum);
      temperature_sensor->publish_state(temp);
    } catch (...) {
      ESP_LOGW("sht30_uart", "Failed to parse line: %s", line.c_str());
    }
  }
}
